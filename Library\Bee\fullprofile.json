{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 540, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 540, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 540, "tid": 74, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 540, "tid": 74, "ts": 1754378947557771, "dur": 3136, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947577222, "dur": 2037, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 540, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 540, "tid": 21474836480, "ts": 1754378865927239, "dur": 472931, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378866400171, "dur": 81131850, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378866400183, "dur": 9782, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378866409975, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378866409982, "dur": 858184, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867268178, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867268184, "dur": 38825, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867307021, "dur": 72, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867307097, "dur": 32057, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867339169, "dur": 74, "ph": "X", "name": "ProcessMessages 20531", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867339247, "dur": 31469, "ph": "X", "name": "ReadAsync 20531", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867370727, "dur": 45, "ph": "X", "name": "ProcessMessages 20490", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867370775, "dur": 38752, "ph": "X", "name": "ReadAsync 20490", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867409538, "dur": 46, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867409587, "dur": 8630, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867418242, "dur": 39, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867418284, "dur": 11497, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867429792, "dur": 50, "ph": "X", "name": "ProcessMessages 20482", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867429847, "dur": 10101, "ph": "X", "name": "ReadAsync 20482", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867439961, "dur": 26, "ph": "X", "name": "ProcessMessages 8101", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867440021, "dur": 6787, "ph": "X", "name": "ReadAsync 8101", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867446819, "dur": 63, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867446887, "dur": 7512, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867454414, "dur": 54, "ph": "X", "name": "ProcessMessages 20528", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867454472, "dur": 12601, "ph": "X", "name": "ReadAsync 20528", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867467084, "dur": 49, "ph": "X", "name": "ProcessMessages 20487", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867467136, "dur": 11145, "ph": "X", "name": "ReadAsync 20487", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867478292, "dur": 44, "ph": "X", "name": "ProcessMessages 20560", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867478339, "dur": 12987, "ph": "X", "name": "ReadAsync 20560", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867491337, "dur": 72, "ph": "X", "name": "ProcessMessages 20484", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867491413, "dur": 42724, "ph": "X", "name": "ReadAsync 20484", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867534148, "dur": 59, "ph": "X", "name": "ProcessMessages 20520", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867534210, "dur": 20400, "ph": "X", "name": "ReadAsync 20520", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867554622, "dur": 28, "ph": "X", "name": "ProcessMessages 8005", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867554667, "dur": 45407, "ph": "X", "name": "ReadAsync 8005", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867600085, "dur": 190, "ph": "X", "name": "ProcessMessages 8455", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867600280, "dur": 23249, "ph": "X", "name": "ReadAsync 8455", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867623541, "dur": 38, "ph": "X", "name": "ProcessMessages 1536", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867623582, "dur": 11316, "ph": "X", "name": "ReadAsync 1536", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867634906, "dur": 11, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867634921, "dur": 14705, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867649638, "dur": 28884, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378867678532, "dur": 3276545, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378870955089, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378870955094, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378870955188, "dur": 33, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378870955223, "dur": 77156, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871032391, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871032397, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871032493, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871032501, "dur": 940915, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871973427, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871973433, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871973537, "dur": 35, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871973576, "dur": 9466, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871983052, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871983057, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871983140, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871983145, "dur": 3643, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871986797, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871986802, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871986839, "dur": 4, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871986845, "dur": 3833, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871990690, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871990695, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871990744, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871990748, "dur": 3484, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871994242, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871994248, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871994296, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871994299, "dur": 3494, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871997813, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871997818, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871997896, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871997899, "dur": 159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871998064, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871998068, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871998132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378871998135, "dur": 3347, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872001493, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872001498, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872001579, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872001583, "dur": 3459, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872005053, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872005058, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872005144, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872005148, "dur": 3677, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872008836, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872008841, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872008918, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872008922, "dur": 384, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872009312, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872009316, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872009376, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872009381, "dur": 3583, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872012975, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872012980, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013071, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013074, "dur": 767, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013850, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013854, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013934, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872013937, "dur": 3176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872017127, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872017133, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872017232, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872017236, "dur": 3724, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872020970, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872020975, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872021071, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872021074, "dur": 3239, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024324, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024330, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024422, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024426, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024627, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024631, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024700, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872024703, "dur": 3902, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872028618, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872028623, "dur": 3780, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872032414, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872032419, "dur": 93, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872032518, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872032522, "dur": 2963, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872035495, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872035500, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872035588, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872035593, "dur": 575, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872036175, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872036179, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872036211, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872036214, "dur": 3769, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872039993, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872039999, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872040068, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872040070, "dur": 3621, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872043701, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872043707, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872043774, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872043776, "dur": 517, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872044300, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872044305, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872044402, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872044406, "dur": 3664, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048081, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048087, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048185, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048190, "dur": 226, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048426, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048522, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872048526, "dur": 3418, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872051955, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872051961, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872052008, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872052012, "dur": 1452, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872053475, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872053480, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872053557, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872053560, "dur": 2630, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872056200, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872056205, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872056285, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872056288, "dur": 1159, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872057456, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872057461, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872057532, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872057535, "dur": 3730, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872061278, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872061284, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872061378, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872061382, "dur": 837, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872062230, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872062235, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872062322, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872062326, "dur": 2952, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872065289, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872065295, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872065383, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872065387, "dur": 830, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872066225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872066229, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872066322, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872066327, "dur": 3161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872069499, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872069504, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872069560, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872069564, "dur": 1363, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872070938, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872070944, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872071050, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872071054, "dur": 2303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872073368, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872073374, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872073457, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872073461, "dur": 1594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872075066, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872075076, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872075159, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872075164, "dur": 2064, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872077239, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872077245, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872077331, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872077335, "dur": 1941, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872079288, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872079294, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872079377, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872079381, "dur": 1536, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872080926, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872080931, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872080987, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872080993, "dur": 2996, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872083999, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872084004, "dur": 1117, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872085132, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872085139, "dur": 1763, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872086911, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872086917, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872086971, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872086974, "dur": 2355, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872089339, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872089344, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872089423, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872089427, "dur": 1176, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872090613, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872090618, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872090698, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872090701, "dur": 2413, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872093124, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872093129, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872093235, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872093238, "dur": 1064, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872094311, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872094319, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872094399, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872094402, "dur": 3309, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872097723, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872097729, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872097786, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872097791, "dur": 4414, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102215, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102221, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102342, "dur": 7, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102353, "dur": 100, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102457, "dur": 4, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102465, "dur": 69, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102539, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102543, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102609, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102613, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102675, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102679, "dur": 55, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102738, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102743, "dur": 68, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102816, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102819, "dur": 57, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102880, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102883, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102947, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872102950, "dur": 126, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103082, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103087, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103161, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103165, "dur": 66, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103237, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103241, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103317, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103322, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103401, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103405, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103488, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103493, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103562, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103565, "dur": 61, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103631, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103638, "dur": 62, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103705, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103708, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103777, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103781, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103822, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103825, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103889, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103892, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103950, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872103952, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104006, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104009, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104062, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104065, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104122, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104125, "dur": 69, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104199, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104203, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104269, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104273, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104339, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104343, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104406, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104409, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104467, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104470, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104535, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104538, "dur": 71, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104613, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104616, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104676, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104680, "dur": 53, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104737, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104739, "dur": 65, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104809, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104813, "dur": 69, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104886, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104889, "dur": 72, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104966, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872104971, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105045, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105049, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105124, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105128, "dur": 70, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105202, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105207, "dur": 67, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105281, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105284, "dur": 100, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105389, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105392, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105463, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105467, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105533, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105537, "dur": 75, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105616, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105619, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105684, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105687, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105744, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105747, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105806, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105810, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105866, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105868, "dur": 60, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105933, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872105935, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106000, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106003, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106076, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106153, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106157, "dur": 70, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106232, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106236, "dur": 76, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106317, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106322, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106401, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106405, "dur": 74, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106484, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106488, "dur": 57, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106549, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872106552, "dur": 1429, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872107992, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872107997, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108037, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108040, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108153, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108156, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108242, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872108247, "dur": 442955, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872551214, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872551220, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872551317, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378872551325, "dur": 6799133, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879350471, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879350479, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879350537, "dur": 40, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879350581, "dur": 22121, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879372713, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879372721, "dur": 120, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879372849, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879372857, "dur": 3893, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879376762, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879376767, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879376820, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378879376853, "dur": 1020574, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880397438, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880397445, "dur": 114, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880397564, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880397571, "dur": 2699, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400281, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400287, "dur": 107, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400399, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400403, "dur": 72, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400481, "dur": 30, "ph": "X", "name": "ProcessMessages 10", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378880400515, "dur": 25938532, "ph": "X", "name": "ReadAsync 10", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378906339059, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378906339064, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378906339132, "dur": 27, "ph": "X", "name": "ProcessMessages 335", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378906339162, "dur": 1911448, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378908250622, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378908250628, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378908250693, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378908250701, "dur": 39013587, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947264302, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947264311, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947264380, "dur": 44, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947264426, "dur": 27355, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947291796, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947291804, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947291923, "dur": 8, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947291935, "dur": 1550, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293496, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293502, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293615, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293618, "dur": 84, "ph": "X", "name": "ReadAsync 24", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293709, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293822, "dur": 1, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293825, "dur": 79, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293909, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293912, "dur": 82, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947293999, "dur": 31, "ph": "X", "name": "ProcessMessages 1", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947294033, "dur": 205536, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947499580, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947499589, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947499683, "dur": 5, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947499691, "dur": 2066, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947501768, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947501774, "dur": 498, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947502283, "dur": 60, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947502349, "dur": 1053, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947503417, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947503422, "dur": 218, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947503649, "dur": 3, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 540, "tid": 21474836480, "ts": 1754378947503656, "dur": 28347, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947579270, "dur": 2381, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 540, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 540, "tid": 17179869184, "ts": 1754378865927155, "dur": 7, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 540, "tid": 17179869184, "ts": 1754378865927163, "dur": 472792, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 540, "tid": 17179869184, "ts": 1754378866399957, "dur": 9929, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947581655, "dur": 40, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 540, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 540, "tid": 1, "ts": 1754378848300032, "dur": 198410, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 540, "tid": 1, "ts": 1754378848498450, "dur": 1244296, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 540, "tid": 1, "ts": 1754378849742779, "dur": 5052662, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947581698, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 540, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848289854, "dur": 315978, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848605837, "dur": 6280149, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848623728, "dur": 66703, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848690449, "dur": 26267, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848716729, "dur": 1443, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848718187, "dur": 102, "ph": "X", "name": "ProcessMessages 20532", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848718294, "dur": 11762, "ph": "X", "name": "ReadAsync 20532", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848730071, "dur": 14, "ph": "X", "name": "ProcessMessages 3561", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848730089, "dur": 1444, "ph": "X", "name": "ReadAsync 3561", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848731544, "dur": 63, "ph": "X", "name": "ProcessMessages 20518", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848731611, "dur": 1104, "ph": "X", "name": "ReadAsync 20518", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848732726, "dur": 11, "ph": "X", "name": "ProcessMessages 3723", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848732740, "dur": 16790, "ph": "X", "name": "ReadAsync 3723", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848749542, "dur": 13, "ph": "X", "name": "ProcessMessages 4382", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848749557, "dur": 1289, "ph": "X", "name": "ReadAsync 4382", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848750857, "dur": 63, "ph": "X", "name": "ProcessMessages 20497", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848750925, "dur": 6188, "ph": "X", "name": "ReadAsync 20497", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848757128, "dur": 13, "ph": "X", "name": "ProcessMessages 3505", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848757143, "dur": 3884, "ph": "X", "name": "ReadAsync 3505", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761041, "dur": 63, "ph": "X", "name": "ProcessMessages 20569", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761108, "dur": 333, "ph": "X", "name": "ReadAsync 20569", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761451, "dur": 30, "ph": "X", "name": "ProcessMessages 7945", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761484, "dur": 94, "ph": "X", "name": "ReadAsync 7945", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761584, "dur": 3, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761588, "dur": 34, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761625, "dur": 4, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761632, "dur": 223, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761861, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761865, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761953, "dur": 4, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848761960, "dur": 82, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762046, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762050, "dur": 61, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762115, "dur": 1, "ph": "X", "name": "ProcessMessages 117", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762117, "dur": 74, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762196, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762199, "dur": 70, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762273, "dur": 4, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762279, "dur": 62, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762345, "dur": 1, "ph": "X", "name": "ProcessMessages 169", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762348, "dur": 149, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762503, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762506, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762581, "dur": 2, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762585, "dur": 99, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762689, "dur": 3, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762694, "dur": 74, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762774, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762779, "dur": 55, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762838, "dur": 3, "ph": "X", "name": "ProcessMessages 211", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762844, "dur": 65, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762913, "dur": 2, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762918, "dur": 56, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762979, "dur": 2, "ph": "X", "name": "ProcessMessages 290", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848762983, "dur": 69, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763056, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763060, "dur": 56, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763120, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763124, "dur": 100, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763229, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763233, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763322, "dur": 1, "ph": "X", "name": "ProcessMessages 163", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763340, "dur": 65, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763409, "dur": 2, "ph": "X", "name": "ProcessMessages 713", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763413, "dur": 53, "ph": "X", "name": "ReadAsync 713", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763470, "dur": 1, "ph": "X", "name": "ProcessMessages 126", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848763473, "dur": 10022, "ph": "X", "name": "ReadAsync 126", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848773507, "dur": 5, "ph": "X", "name": "ProcessMessages 104", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848773514, "dur": 3507, "ph": "X", "name": "ReadAsync 104", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777032, "dur": 40, "ph": "X", "name": "ProcessMessages 20535", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777075, "dur": 223, "ph": "X", "name": "ReadAsync 20535", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777305, "dur": 11, "ph": "X", "name": "ProcessMessages 3703", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777319, "dur": 78, "ph": "X", "name": "ReadAsync 3703", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777402, "dur": 2, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777406, "dur": 69, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777480, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777484, "dur": 69, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777558, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777562, "dur": 56, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777622, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848777626, "dur": 466, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778101, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778107, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778199, "dur": 3, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778205, "dur": 80, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778290, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778308, "dur": 72, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778385, "dur": 2, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778388, "dur": 285, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778680, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778684, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778758, "dur": 2, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778763, "dur": 70, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778838, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778842, "dur": 67, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778913, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778918, "dur": 41, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778962, "dur": 2, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848778966, "dur": 60, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779030, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779034, "dur": 84, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779122, "dur": 2, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779126, "dur": 77, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779208, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779212, "dur": 60, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779276, "dur": 1, "ph": "X", "name": "ProcessMessages 206", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779280, "dur": 88, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779373, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779377, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779472, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779476, "dur": 253, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779736, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779740, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779813, "dur": 2, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779818, "dur": 62, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779884, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779888, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779966, "dur": 2, "ph": "X", "name": "ProcessMessages 326", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848779970, "dur": 88, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848780063, "dur": 2, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848780069, "dur": 9981, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848790061, "dur": 4, "ph": "X", "name": "ProcessMessages 747", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848790069, "dur": 5040, "ph": "X", "name": "ReadAsync 747", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848795121, "dur": 69, "ph": "X", "name": "ProcessMessages 20529", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848795194, "dur": 11176, "ph": "X", "name": "ReadAsync 20529", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848806381, "dur": 33, "ph": "X", "name": "ProcessMessages 7764", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848806418, "dur": 16491, "ph": "X", "name": "ReadAsync 7764", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848822926, "dur": 63, "ph": "X", "name": "ProcessMessages 20503", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848822995, "dur": 326, "ph": "X", "name": "ReadAsync 20503", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823329, "dur": 11, "ph": "X", "name": "ProcessMessages 4291", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823343, "dur": 54, "ph": "X", "name": "ReadAsync 4291", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823401, "dur": 4, "ph": "X", "name": "ProcessMessages 805", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823408, "dur": 61, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823474, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823482, "dur": 25, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823510, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823512, "dur": 144, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823663, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823667, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823770, "dur": 2, "ph": "X", "name": "ProcessMessages 225", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848823774, "dur": 252, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848824033, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848824037, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848824124, "dur": 2, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848824129, "dur": 1428, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848825572, "dur": 4, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848825579, "dur": 145, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848825732, "dur": 7, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848825742, "dur": 304, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826052, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826058, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826156, "dur": 3, "ph": "X", "name": "ProcessMessages 581", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826161, "dur": 122, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826289, "dur": 3, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826294, "dur": 330, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826631, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826636, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826723, "dur": 4, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826730, "dur": 86, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826822, "dur": 2, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826827, "dur": 77, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826909, "dur": 3, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848826914, "dur": 396, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827320, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827325, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827423, "dur": 3, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827428, "dur": 86, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827519, "dur": 2, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827524, "dur": 80, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827608, "dur": 2, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827615, "dur": 79, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827699, "dur": 2, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827703, "dur": 79, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827787, "dur": 2, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827791, "dur": 47, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827841, "dur": 1, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827844, "dur": 74, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827922, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827926, "dur": 64, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827995, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848827998, "dur": 91, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828094, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828097, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828174, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828177, "dur": 352, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828542, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828624, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828629, "dur": 65, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828698, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828702, "dur": 85, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828792, "dur": 1, "ph": "X", "name": "ProcessMessages 181", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828795, "dur": 67, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828868, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848828872, "dur": 25552, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848854434, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848854441, "dur": 26435, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848880889, "dur": 43, "ph": "X", "name": "ProcessMessages 20506", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848880936, "dur": 19154, "ph": "X", "name": "ReadAsync 20506", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848900109, "dur": 21302, "ph": "X", "name": "ProcessMessages 15194", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848921453, "dur": 65102, "ph": "X", "name": "ReadAsync 15194", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378848986571, "dur": 47046, "ph": "X", "name": "ProcessMessages 7989", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849033628, "dur": 153, "ph": "X", "name": "ReadAsync 7989", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849033785, "dur": 27, "ph": "X", "name": "ProcessMessages 1812", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849033814, "dur": 714, "ph": "X", "name": "ReadAsync 1812", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849034538, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849034542, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849034631, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849034636, "dur": 849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849035495, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849035500, "dur": 109, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849035614, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849035617, "dur": 778, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849036404, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849036408, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849036501, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849036505, "dur": 810, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849037324, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849037329, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849037367, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849037370, "dur": 883, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849038263, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849038268, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849038313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849038316, "dur": 849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849039173, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849039178, "dur": 728, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849039917, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849039922, "dur": 823, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849040756, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849040761, "dur": 1942, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849042718, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849042723, "dur": 888, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849043622, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849043627, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849043699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849043702, "dur": 872, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849044593, "dur": 6, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849044601, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849044680, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849044684, "dur": 829, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849045522, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849045527, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849045632, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849045636, "dur": 26596, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849072245, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849072250, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849072333, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849072337, "dur": 1169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849073516, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849073521, "dur": 137, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849073664, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849073667, "dur": 3082, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849076761, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849076770, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849076855, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849076860, "dur": 304, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849077172, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849077176, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849077211, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849077213, "dur": 952, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849078174, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849078178, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849078254, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849078259, "dur": 922, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849079189, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849079193, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849079250, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849079252, "dur": 1747, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081009, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081014, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081086, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081090, "dur": 865, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081965, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849081970, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849082050, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849082053, "dur": 1133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083198, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083204, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083287, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083290, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083507, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083510, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083567, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083569, "dur": 275, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083852, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083856, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849083897, "dur": 1141, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849085050, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849085055, "dur": 368, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849085432, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849085437, "dur": 626, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086074, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086080, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086159, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086164, "dur": 70, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086239, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086242, "dur": 56, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086303, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849086307, "dur": 8084, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849094399, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849094404, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849094481, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378849094485, "dur": 2345172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378851439669, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378851439675, "dur": 221, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378851439904, "dur": 33227, "ph": "X", "name": "ProcessMessages 467", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378851473141, "dur": 2025473, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853498630, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853498637, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853498729, "dur": 35, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853498767, "dur": 26185, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853524964, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853524970, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853525062, "dur": 6, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853525071, "dur": 7272, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532355, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532362, "dur": 396, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532768, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532774, "dur": 56, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532832, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532838, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853532860, "dur": 10507, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543389, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543403, "dur": 149, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543559, "dur": 4, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543565, "dur": 235, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543806, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543809, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543889, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853543893, "dur": 2516, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853546426, "dur": 9, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853546438, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853546480, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853546483, "dur": 1409, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853547901, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853547906, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853547957, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853547960, "dur": 2395, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853550365, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853550370, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853550430, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853550433, "dur": 1263, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853551703, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853551707, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853551742, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853551746, "dur": 3037, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853554795, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853554800, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853554837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853554840, "dur": 479, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555323, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555326, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555351, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555354, "dur": 474, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555836, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555864, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853555866, "dur": 3497, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853559373, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853559377, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853559413, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853559415, "dur": 985, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853560407, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853560411, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853560441, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853560443, "dur": 796, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853561245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853561248, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853561276, "dur": 1898, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853563185, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853563189, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853563233, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853563235, "dur": 1656, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853564900, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853564905, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853564980, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853564984, "dur": 2084, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567078, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567084, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567156, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567159, "dur": 305, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567470, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567473, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567537, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853567540, "dur": 1136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853568684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853568688, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853568763, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853568767, "dur": 808, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853569581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853569584, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853569671, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853569675, "dur": 1840, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853571523, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853571528, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853571604, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853571608, "dur": 740, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853572354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853572357, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853572397, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853572400, "dur": 3111, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853575520, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853575525, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853575590, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853575595, "dur": 1613, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577218, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577260, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577262, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577334, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577386, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853577390, "dur": 1618, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579016, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579019, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579108, "dur": 404, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579516, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579519, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579573, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853579576, "dur": 1495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853581081, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853581085, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853581159, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853581162, "dur": 2344, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853583517, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853583521, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853583579, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853583582, "dur": 4494, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588085, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588090, "dur": 139, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588233, "dur": 29, "ph": "X", "name": "ProcessMessages 2004", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588264, "dur": 89, "ph": "X", "name": "ReadAsync 2004", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588358, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588362, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588401, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588406, "dur": 74, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588487, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588492, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588586, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378853588590, "dur": 1295299, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378854883901, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378854883908, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378854884001, "dur": 754, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 540, "tid": 12884901888, "ts": 1754378854884767, "dur": 330, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947581708, "dur": 1419, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 540, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 540, "tid": 8589934592, "ts": 1754378848281432, "dur": 6514053, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 540, "tid": 8589934592, "ts": 1754378854795488, "dur": 11, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 540, "tid": 8589934592, "ts": 1754378854795502, "dur": 35573, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947583134, "dur": 26, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 540, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 540, "tid": 4294967296, "ts": 1754378848212064, "dur": 6820420, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378848232282, "dur": 31963, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378855035377, "dur": 10768376, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378865866400, "dur": 81665720, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378865866645, "dur": 31678, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378947532178, "dur": 20184, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378947547026, "dur": 61, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 540, "tid": 4294967296, "ts": 1754378947552382, "dur": 48, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947583164, "dur": 49, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754378866386245, "dur": 117, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378866386448, "dur": 845010, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378867231468, "dur": 868, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378867232667, "dur": 234, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754378867232901, "dur": 442, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378867234093, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_BE26794A23ED3DDD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867234594, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_3F7D13D5F95F7D47.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867235065, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_9284D96A50868F4F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867235325, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_7B48CBC95104A100.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867235986, "dur": 1358, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_154BFD7FC1A8A7AB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867237509, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_836646C75BC63239.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867237578, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_F8767C8AC05BA5DD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867237659, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_5F5ED9859A7884C1.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867237741, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_1D276C237AA35AED.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867237839, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_1936861B41E20424.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867238234, "dur": 3078, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_CF94F87C2BF74454.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867241505, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_9694BB859CDBC32D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867241745, "dur": 25108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_30D61BF1868D39BE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867267215, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_81714D197E7394C2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867267425, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_FCD037BA9EE2A890.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867268421, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_734B28A34D825150.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867268615, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8AEA7B1CC3A388D2.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867269384, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6AC22714CA32D3A7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867270035, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867270390, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867270508, "dur": 35313, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867306828, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867307294, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867307850, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867309912, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867310109, "dur": 27858, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867339533, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Profiling.Core.ref.dll_DDC12D7300735955.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867340101, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867340261, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867341033, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867341251, "dur": 231, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867341635, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ResourceManager.ref.dll_56EDCE417F1B8EC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867341896, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867342530, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867343201, "dur": 26356, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867370021, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867371861, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867373200, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867373422, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867374028, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867374240, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_5436F99F8BDCED8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867374330, "dur": 33917, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867408361, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867408473, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867409556, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867409812, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867410722, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378867410971, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Runtime.ref.dll_5400A10AF7CC6BEA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867411698, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867412302, "dur": 4559, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867417745, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378867418267, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867418505, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D60199621A6AEC70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867420514, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378867420907, "dur": 7306, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867428510, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867429026, "dur": 103, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867430467, "dur": 8347, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867438834, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867439168, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867440984, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867442990, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867443331, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867444343, "dur": 1175, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867446051, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867446328, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867449083, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867450590, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8863518860715653438.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867451682, "dur": 1529, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867453279, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378867453483, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867454371, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867455169, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867455852, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867456010, "dur": 112, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12743953686143551850.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867456688, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867456748, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867457682, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867458708, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Tests.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867459139, "dur": 6650, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867466560, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867466824, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378867469111, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867470874, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867472419, "dur": 4590, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867477289, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378867478833, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867479000, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13958411980936867695.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867480362, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867480424, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867482423, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378867482488, "dur": 7614, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378867490720, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378867491499, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378867233404, "dur": 258882, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378867492329, "dur": 80007594, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947499932, "dur": 370, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947500303, "dur": 129, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947500433, "dur": 76, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947500659, "dur": 154, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947500883, "dur": 4408, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754378867427580, "dur": 64862, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867492506, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867492646, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D728BD1B62B4CC1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867492986, "dur": 70, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_CF94F87C2BF74454.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867493224, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_9694BB859CDBC32D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867493597, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_307A68DDD2E77CDB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867493729, "dur": 1954, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B480A183C8985E4E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867496042, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_C205E8D963897709.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867496290, "dur": 1914, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_B812FDC44B22C083.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867498276, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867498635, "dur": 10676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867509438, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867509819, "dur": 9535, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867519493, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867520176, "dur": 1889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867522067, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867522223, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867522557, "dur": 1883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867524442, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867524577, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867525017, "dur": 1745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867526764, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867526877, "dur": 365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867527269, "dur": 383, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867527683, "dur": 1231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867529005, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867529427, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867529684, "dur": 2084, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867531831, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867532157, "dur": 43825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867576071, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867576354, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867576413, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867576501, "dur": 9138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867585641, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867585758, "dur": 395, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867586202, "dur": 1594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867587886, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867588177, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867588458, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867588746, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867589121, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867589509, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867589808, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867590147, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867590436, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867590724, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867591008, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867591370, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867591646, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867591927, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867592205, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867592545, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867592819, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867593106, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867593881, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867593957, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867594701, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867594779, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867595526, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867595594, "dur": 837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867596432, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867596499, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867597393, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867597481, "dur": 922, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867598405, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867598492, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867599397, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867599468, "dur": 1173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867600642, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867600712, "dur": 779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867601492, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867601617, "dur": 1627, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867603246, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867603327, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867603706, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378867603990, "dur": 18403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378867622396, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378867624871, "dur": 4352451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871977323, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871980927, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871981055, "dur": 3623, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871984680, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871984801, "dur": 3811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871988672, "dur": 3431, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871992106, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871992235, "dur": 3754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871995991, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871996066, "dur": 3281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378871999350, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378871999466, "dur": 3558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872003026, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872003098, "dur": 3618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872006719, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872006811, "dur": 4053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872010866, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872010957, "dur": 4050, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872015010, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872015106, "dur": 3817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872018927, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872018995, "dur": 3556, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872022562, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872022640, "dur": 3945, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872026587, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872026820, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872030438, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872030505, "dur": 3575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872034083, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872034173, "dur": 3670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872037845, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872037958, "dur": 3612, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872041572, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872041674, "dur": 4269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872045946, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872046051, "dur": 3775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872049829, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872049902, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872049966, "dur": 4113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872054082, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872054175, "dur": 4960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872059138, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872059217, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872059311, "dur": 3853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872063167, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872063258, "dur": 4105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872067366, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872067472, "dur": 3772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872071248, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872071345, "dur": 3780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872075128, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872075246, "dur": 3569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872078818, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872078916, "dur": 5333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872084252, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872084336, "dur": 4145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872088483, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872088571, "dur": 3639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872092212, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872092296, "dur": 3314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872095613, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872095672, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872095728, "dur": 4381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378872100112, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872101037, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754378872103360, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754378872104004, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872104542, "dur": 1480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378872106064, "dur": 7264221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378879370320, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754378879370288, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754378879370708, "dur": 4049, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.Runtime.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754378879374764, "dur": 68125075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867427580, "dur": 64803, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867494747, "dur": 253, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1754378867495001, "dur": 2272, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1754378867497273, "dur": 132, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1754378867492403, "dur": 5003, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867497407, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867497610, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867497858, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867498558, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867499003, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867499321, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867499984, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867500278, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867500754, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867500832, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867501367, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867501562, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867502117, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867502883, "dur": 649, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867503620, "dur": 2060, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867506102, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867506451, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867506695, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867507112, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14922049101417115840.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867507765, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867507917, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378867508025, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867508565, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867508963, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867509275, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867509698, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867510027, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867510343, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867510663, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867510991, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867511344, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867511664, "dur": 425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867512090, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867512419, "dur": 471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867512890, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867513221, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867513530, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867513820, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867514063, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867514377, "dur": 330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867514707, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867515049, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867515484, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867515823, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867516189, "dur": 1078, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\AssetPostProcessors\\MaterialPostprocessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754378867516152, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867517640, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867517971, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867518309, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867518631, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867518955, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867519383, "dur": 507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867519890, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867520201, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867520516, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867520870, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867521231, "dur": 618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867521850, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867522231, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378867522592, "dur": 2500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378867525190, "dur": 353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378867525569, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378867527924, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378867528213, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378867529063, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867529184, "dur": 10594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754378867539781, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867539844, "dur": 282, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378867540151, "dur": 4431273, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1754378871977310, "dur": 3624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871980936, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871981050, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871984680, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871984752, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871984833, "dur": 3650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871988486, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871988611, "dur": 3522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871992135, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871992190, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871992248, "dur": 3407, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871995657, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871995777, "dur": 3625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378871999404, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378871999483, "dur": 3334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872002820, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872003043, "dur": 4188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872007234, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872007325, "dur": 4440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872011767, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872011851, "dur": 3271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872015124, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872015187, "dur": 3664, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872018854, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872018948, "dur": 3240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872022191, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872022302, "dur": 3741, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872026053, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872026250, "dur": 3503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872029755, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872029850, "dur": 3518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872033370, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872033471, "dur": 8740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872042213, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872042281, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872042426, "dur": 3929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872046357, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872046416, "dur": 123, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872046546, "dur": 4803, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872051351, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872051451, "dur": 3905, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872055358, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872055436, "dur": 4668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872060107, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872060208, "dur": 3930, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872064140, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872064217, "dur": 4547, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872068767, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872068889, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872069072, "dur": 3884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872072958, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872073049, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872077180, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872077263, "dur": 4611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872081876, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872081957, "dur": 1298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872083263, "dur": 3933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872087199, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872087318, "dur": 3703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872091024, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872091107, "dur": 4504, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872095613, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872095716, "dur": 10164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378872105883, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378872106148, "dur": 75183179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378947289386, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754378947289331, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754378947291447, "dur": 711, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378947289730, "dur": 2634, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754378947292372, "dur": 207427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867427621, "dur": 65100, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867492742, "dur": 1026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2E5F03CD10620934.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867495373, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867497150, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867497248, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867497941, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867499203, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867499487, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867499979, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867500112, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867500261, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754378867500610, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867500767, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867501385, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867501479, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867501567, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867503500, "dur": 793, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867504312, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867505103, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867505247, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867505559, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867505914, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867506244, "dur": 1856, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867508102, "dur": 763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867508866, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867509198, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867509546, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867509878, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867510201, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867510523, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867510841, "dur": 340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867511181, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867511512, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867511826, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867512154, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867512467, "dur": 770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867513238, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867513616, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867513938, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867514261, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867514592, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867514935, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867515343, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867515712, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867516184, "dur": 1060, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Targets\\Fullscreen\\FullscreenMetaData.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754378867516045, "dur": 1493, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867517539, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867517886, "dur": 327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867518214, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867518561, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867518874, "dur": 544, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867520481, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867520922, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867521263, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867521619, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867521961, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867522314, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867522636, "dur": 8446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867531180, "dur": 9738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867540967, "dur": 7061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867548141, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867548509, "dur": 1570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867550135, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867550435, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867551410, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867551555, "dur": 291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867551890, "dur": 4112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867556103, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867556444, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867556781, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867557606, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867557691, "dur": 2928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867560622, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867560751, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867561103, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867561418, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867561723, "dur": 2135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867563917, "dur": 929, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867564848, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867564918, "dur": 2965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867567995, "dur": 1651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867569649, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867569735, "dur": 4120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867573904, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867573969, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867574280, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867574394, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867575256, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867575337, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378867576778, "dur": 48520, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378867575655, "dur": 49645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378867625310, "dur": 180, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378867625636, "dur": 3327447, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378870958907, "dur": 70559, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.Runtime.ref.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754378870958094, "dur": 71402, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378871030153, "dur": 146, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378871031525, "dur": 35305541, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378906341624, "dur": 1768325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754378906341603, "dur": 1848327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378908247332, "dur": 1237, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378908290147, "dur": 38972122, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378947289342, "dur": 208146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754378947289299, "dur": 208192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754378947497508, "dur": 134, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378947497647, "dur": 2113, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754378867427634, "dur": 65679, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867493315, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_852883CBC406BC18.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867493598, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867495173, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_2C5395855EB59FDC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867495372, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3306460A01459EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867495930, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_72D94C2637730948.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867496077, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_FC21CA729C9E8F45.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867496597, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867497027, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867497433, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867497516, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867497772, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867498007, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867498180, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754378867498261, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867498574, "dur": 4892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867503468, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867503631, "dur": 1060, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867505028, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867505490, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867505566, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867506079, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8908569252755618054.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867507264, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7902948547077102519.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378867507935, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867508620, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Tests\\Editor\\DocExampleCode\\ScriptReference\\UsingAssetRefSpriteValidateAsset.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378867508354, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867509421, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754378867509585, "dur": 309, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867509894, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867510171, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867510501, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867510920, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867511252, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867511584, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867511922, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867512224, "dur": 440, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867512665, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867513003, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867513322, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867513637, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867513973, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867514306, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867514645, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867514988, "dur": 420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867515409, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867515748, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867516199, "dur": 1104, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\BaseShaderGUI.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378867516078, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867517555, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867517887, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867518210, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867518536, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867518889, "dur": 648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867519607, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867519927, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867520211, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867520487, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867520786, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867521058, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867521362, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867521728, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867522067, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867522385, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867522697, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867523719, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867524088, "dur": 2182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867526331, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867526645, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867527740, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867528735, "dur": 307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867529078, "dur": 10745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867539825, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867540215, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867541975, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867542367, "dur": 338, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867542744, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867543138, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867544077, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867544150, "dur": 1066, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867545282, "dur": 3003, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867548338, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867548646, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867548949, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867549977, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867550898, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94F5458D7254E99F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867551177, "dur": 301, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867551520, "dur": 4453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867556073, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867556402, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867556708, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867557007, "dur": 877, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867557886, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867557960, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867558971, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867559052, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867559881, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867560234, "dur": 302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867560574, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867560875, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867560954, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867561250, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867561560, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867561801, "dur": 1449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867563311, "dur": 7167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867570525, "dur": 3430, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867573961, "dur": 1344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867575618, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867575978, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867576308, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867576608, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867576700, "dur": 10588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867587392, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867587840, "dur": 4254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867592192, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867592487, "dur": 1495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867593984, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867594091, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378867594424, "dur": 919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867595345, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867595429, "dur": 1049, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867596480, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867596547, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867597481, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867597546, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867598405, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867598527, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867599453, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867599513, "dur": 863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867600379, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867600447, "dur": 918, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867601366, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867601439, "dur": 1253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867602694, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867602794, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867603973, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867604043, "dur": 1177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378867605224, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867606719, "dur": 18824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378867625545, "dur": 4351772, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378871977322, "dur": 3608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378871980932, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378871981012, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378871981072, "dur": 3601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378871984676, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378871984758, "dur": 81, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378871984843, "dur": 3656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378871988501, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378871988648, "dur": 34804, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754378871988597, "dur": 41095, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378872548505, "dur": 624, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378872550212, "dur": 6798269, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378879370322, "dur": 1025034, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754378879370288, "dur": 1025070, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754378880395376, "dur": 190, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378880398233, "dur": 193, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378880395572, "dur": 2924, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754378880398502, "dur": 67101321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378947523826, "dur": 4956, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1754378857757315, "dur": 7053724, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378857760114, "dur": 701147, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378862552457, "dur": 18237, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378862570702, "dur": 2240314, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378862575508, "dur": 754212, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378865464847, "dur": 102674, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1754378865440197, "dur": 127991, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754378848601164, "dur": 326, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378848601604, "dur": 4823, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378848606465, "dur": 3807, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378848610742, "dur": 303, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754378848611047, "dur": 509, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378848612252, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_46C68156E74BD305.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848613454, "dur": 128, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_09B5031AFD9FEC01.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848613635, "dur": 201, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_11B3DED74274F630.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848615479, "dur": 50488, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_30D61BF1868D39BE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848666142, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_52CED5B2AAF4C2CF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848666750, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_E1EFA01C71B1D523.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848666810, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_0939C2A13B34B768.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848667190, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_10E454BD63844E1A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848668235, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_C5808661E52B4BBA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848668741, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Android.Extensions.dll_4BA974FBC191E139.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848668981, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848669520, "dur": 48383, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848718679, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848719200, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848719733, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848720188, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848721069, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848721342, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_407F07063643C512.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848721544, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848721839, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.ref.dll_F30DFA20C0DD1968.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848722012, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848722745, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848722862, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848723021, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848723354, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848723534, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848723960, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848724647, "dur": 6645, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848731961, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Profiling.Core.ref.dll_DDC12D7300735955.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848732197, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848732743, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848732939, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848733281, "dur": 166, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848733556, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848734406, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848735779, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848736379, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848736922, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.ref.dll_CE6A42C97D96EB0A.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848737002, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848738418, "dur": 12260, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848751177, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848751432, "dur": 138, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848752392, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848752452, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848753377, "dur": 129, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848753578, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848754067, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848754468, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848755826, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848756027, "dur": 3727, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848759800, "dur": 120, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848760134, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848760733, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848761646, "dur": 290, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762014, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762190, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762366, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762450, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762715, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848762958, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_FAA396FB17B8C1A8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763027, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763114, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763202, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763286, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763386, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763487, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763665, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D60199621A6AEC70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763730, "dur": 125, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848763865, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848764020, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848764354, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Editor.ref.dll_0F50152946DB09D0.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848764425, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848765027, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848766673, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848767012, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848767425, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848767553, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 0, "ts": 1754378848768109, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848768782, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848769258, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848770172, "dur": 4560, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848775826, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848775894, "dur": 237, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848776663, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848776776, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848777332, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848777840, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848777913, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848777991, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848778512, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848778622, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848778715, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3232911574759799904.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779121, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Profiling.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779227, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779285, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779362, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779436, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779566, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779651, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848779829, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848780165, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848780407, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848780501, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848780571, "dur": 109, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848780989, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848781982, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848782498, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848782556, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848783521, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848784532, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848784983, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848785088, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848786064, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848786268, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848787550, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848787608, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848788000, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848788107, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848788165, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848788379, "dur": 2860, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848791549, "dur": 172, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848792695, "dur": 265, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848793066, "dur": 139, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848793215, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2157608619508796868.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848793670, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848793783, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848793962, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848794724, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848794864, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848795042, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848795177, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848795740, "dur": 1116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848796968, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848797035, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848797263, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848797838, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.TestFramework.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848797901, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848797964, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848798537, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848798607, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848799020, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10831531723267835799.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848799569, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848800045, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848800588, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848801043, "dur": 101, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848801552, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848801659, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848801832, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848802514, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848803079, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Tests.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848803154, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848804200, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848804370, "dur": 17378, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848822585, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Updater.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848822695, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848822755, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654825778349625970.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848823369, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848823536, "dur": 204, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848823901, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848824086, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6262281476893245489.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848824466, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848824584, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848824652, "dur": 124, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848825722, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848825836, "dur": 102, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848825949, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848826457, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848826576, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827058, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827212, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827301, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827739, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827861, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848827932, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828025, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828111, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828195, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828285, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828548, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12436201776501168220.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848828957, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848829065, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848829196, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7168266853217099875.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848829639, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Addressables.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848829755, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848830299, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848830806, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848831356, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848831728, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848831836, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848832327, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848833440, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848833510, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848833575, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848834317, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848834452, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848834527, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848835449, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848835733, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848836458, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848836910, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848837530, "dur": 18041, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848855585, "dur": 212, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848856690, "dur": 108, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848857242, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848857785, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848858718, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848858775, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848858839, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848859355, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754378848859416, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848859534, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754378848859656, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754378848859714, "dur": 116, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754378848860441, "dur": 98, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754378848860592, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754378848861398, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754378848611619, "dur": 250294, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378848861951, "dur": 5969401, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378854831458, "dur": 160, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378854831696, "dur": 3393, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754378848675765, "dur": 186322, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848862156, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848862362, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D728BD1B62B4CC1C.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848865753, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_66765A4F32C0DF20.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848866773, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_AE211F434B9B95F3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848866994, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_154BFD7FC1A8A7AB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848868323, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_917905B6D303F6E8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848869428, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_DA6D0FDBE00A486D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848869584, "dur": 410, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_11B3DED74274F630.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848870069, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848870350, "dur": 14698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848885049, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848885177, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848885563, "dur": 15951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848901645, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848902075, "dur": 2013, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848904089, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848904231, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848904575, "dur": 1709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848906402, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848906723, "dur": 810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848907534, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848907603, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848907985, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848910402, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848910729, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848911058, "dur": 1107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848912230, "dur": 62320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848974553, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848974622, "dur": 2337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.ref.dll_1E6ED5409D07C9A3.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848976961, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848977084, "dur": 745, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848977831, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848977993, "dur": 1348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848979410, "dur": 1473, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848981036, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848981382, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848981687, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848981989, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848983021, "dur": 885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848983966, "dur": 7114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848991150, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848994775, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848995223, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848995345, "dur": 478, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848995907, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378848996519, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848996597, "dur": 1196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848997859, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848998886, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378848999738, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378848999804, "dur": 6411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849006217, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849006621, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849007056, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849007463, "dur": 360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849007852, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849010050, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849010146, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849010445, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849010513, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849010856, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849011209, "dur": 1769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849013039, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849015312, "dur": 61801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849077226, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849077636, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849078503, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849078615, "dur": 894, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849079512, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849079637, "dur": 1679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849081318, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849081449, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849082278, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849082394, "dur": 1114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849083511, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849083627, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849083906, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849083982, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754378849084299, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849085263, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849085354, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849085417, "dur": 953, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754378849086373, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849087945, "dur": 67, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849090203, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378849090503, "dur": 4425426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853515932, "dur": 3779, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853519713, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853519777, "dur": 3495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853523324, "dur": 6721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853530106, "dur": 13678, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853543786, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853544218, "dur": 3960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853548270, "dur": 3765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853552046, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853552165, "dur": 3572, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853555739, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853555809, "dur": 4963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853560774, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853560860, "dur": 9101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853569971, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853570025, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853570153, "dur": 5656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853575812, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853575983, "dur": 3955, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853580007, "dur": 3840, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853583850, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853583954, "dur": 3653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754378853588507, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754378853588901, "dur": 1242099, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848675893, "dur": 193472, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848869394, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_9284D96A50868F4F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848869587, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848869842, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848869943, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_05415FEA72405DAF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848870833, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848871231, "dur": 5465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848876698, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848876958, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848877293, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848877698, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848878082, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848878148, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848878384, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848878528, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848878740, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848879163, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848879744, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848880122, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848881023, "dur": 968, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1754378848882770, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848883150, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848883260, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848883688, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848883795, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848884532, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848885202, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848886004, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848886411, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14922049101417115840.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848886652, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848887073, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7168266853217099875.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754378848887399, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848887733, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848888065, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848888371, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848888659, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848888957, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848889254, "dur": 358, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848889613, "dur": 351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848889965, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848890262, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848890567, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848890882, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848891707, "dur": 505, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\Tests\\TestFixture\\ScopedDisposable.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754378848891257, "dur": 1019, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848892277, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848892750, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848893127, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848893437, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848893723, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848894019, "dur": 290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848894310, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848894570, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848894826, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848895122, "dur": 331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848895453, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848895807, "dur": 296, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848896104, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848896378, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848896682, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848897054, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848897363, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848897642, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848897945, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848898284, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848898646, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848898993, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848899347, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848899784, "dur": 1074, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\2D\\ShaderGraph\\AssetCallbacks\\CreateSpriteLitShaderGraph.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754378848899717, "dur": 1451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848901168, "dur": 353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848901522, "dur": 363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848901886, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848902236, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848902641, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848903061, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848903368, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848903715, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848904057, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848904372, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848904701, "dur": 891, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848905692, "dur": 357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848906096, "dur": 7365, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848913463, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848913531, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848913908, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378848913984, "dur": 2510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848916658, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848917046, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848917403, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848919720, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848920028, "dur": 13641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848933771, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848934099, "dur": 10019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848944225, "dur": 4474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378848948768, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378848949096, "dur": 63750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849012937, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849013282, "dur": 1815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849015205, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849015583, "dur": 1653, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849017248, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849017327, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849018241, "dur": 1934, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849020243, "dur": 3832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849024076, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849024202, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849024440, "dur": 2045, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849026507, "dur": 434, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849026943, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849027009, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849027462, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849027958, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849028029, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849028432, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849028505, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849028816, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849029131, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849029438, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849029767, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849030095, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849030450, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849030786, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849031101, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849031421, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849031719, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849031999, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849032071, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849032422, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849032733, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754378849033035, "dur": 845, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849033882, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849033977, "dur": 884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849034863, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849034982, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849035793, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849035912, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849036712, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849036833, "dur": 808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849037643, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849037762, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849038590, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849038698, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849039519, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849039621, "dur": 1463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849041087, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849041209, "dur": 2695, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849043907, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849044046, "dur": 846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849044895, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849045013, "dur": 827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849045843, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849045969, "dur": 26575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849072546, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849072649, "dur": 1211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849073862, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849073924, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849074105, "dur": 20611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754378849094719, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378849094838, "dur": 4409788, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853504629, "dur": 5404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853510035, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853510148, "dur": 3505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853513655, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853513721, "dur": 3452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853517235, "dur": 3628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853520921, "dur": 4848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853525771, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853525871, "dur": 3694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853529620, "dur": 3141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853532763, "dur": 575, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853533352, "dur": 3582, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853536936, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853537003, "dur": 3371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853540389, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853540462, "dur": 27361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853567825, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853567935, "dur": 3944, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853571884, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853571981, "dur": 5776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853577824, "dur": 3609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853581435, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853581553, "dur": 3441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853584996, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754378853585062, "dur": 3815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754378853588964, "dur": 1242297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848675929, "dur": 193967, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848869917, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5001ACC648CFBA23.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848870653, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378848873574, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1754378848873843, "dur": 3898, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754378848877746, "dur": 3319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848881066, "dur": 9903, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1754378848890973, "dur": 4401, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Actions\\ClipAction.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754378848890973, "dur": 4721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848895960, "dur": 1421, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754378848895695, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848897420, "dur": 472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848897893, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848898295, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848898653, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848899783, "dur": 1072, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\2D\\ShapeEditor\\EditorTool\\PathEditorToolExtensions.cs"}}, {"pid": 12345, "tid": 3, "ts": 1754378848898996, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848900955, "dur": 394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848901350, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848901794, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848902150, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848902549, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848903010, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848903303, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848903620, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848903908, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848904246, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848906642, "dur": 10951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848917706, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848918064, "dur": 5957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848924150, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848924515, "dur": 4014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848928599, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848928957, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848929257, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848930307, "dur": 965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848931344, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848931679, "dur": 1522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378848933203, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378848933420, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848933868, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378848934171, "dur": 78505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849012781, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378849013190, "dur": 4314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849017506, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378849017615, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378849018061, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849020668, "dur": 987, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849021724, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378849022061, "dur": 824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849022887, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378849022967, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754378849023492, "dur": 951, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378849024452, "dur": 1313, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378849026099, "dur": 2411593, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754378851443163, "dur": 2061458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853504624, "dur": 6105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853510731, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853510795, "dur": 3510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853514365, "dur": 3605, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853518026, "dur": 3662, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853521742, "dur": 6119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853527863, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853527948, "dur": 3603, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853531612, "dur": 4450, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853536064, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853536126, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853536207, "dur": 3552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853539821, "dur": 8350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853548174, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853548228, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853548309, "dur": 3836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853552147, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853552220, "dur": 4017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853556239, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853556316, "dur": 5322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853561640, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853561712, "dur": 3536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853565250, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853565352, "dur": 3691, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853569045, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853569136, "dur": 3602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853572740, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853572850, "dur": 4743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853577595, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853577688, "dur": 8263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754378853585953, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853587326, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853588512, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754378853588987, "dur": 1242118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848676141, "dur": 194379, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848870525, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378848871988, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848872649, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848872846, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848873155, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848873445, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848873713, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848873877, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848874685, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848874772, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848874934, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848875165, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848875292, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848875544, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848875727, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848875921, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848876147, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848876299, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848876633, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848878107, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848878389, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848878656, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848878963, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848879051, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848879130, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848879763, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848879859, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848879945, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848881028, "dur": 1466, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1754378848882641, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848882704, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848883023, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848883459, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848883688, "dur": 2994, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4014436084419441659.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848886878, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754378848887137, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848887385, "dur": 392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848887777, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848888148, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848888471, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848888783, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848889104, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848889417, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848889764, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848890072, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848890444, "dur": 314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848890758, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848891398, "dur": 3657, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.multiplayer.center@f3fb577b3546\\Editor\\MultiplayerCenterWindow\\UI\\RecommendationView\\SolutionSelectionView.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378848891125, "dur": 3981, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848895107, "dur": 726, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848895834, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848896128, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848896421, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848896805, "dur": 1696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Unity.Burst.CodeGen\\BurstILPostProcessor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378848896701, "dur": 1945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848898647, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848899773, "dur": 1058, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@24527717fb47\\Editor\\AssetPostProcessors\\FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378848898992, "dur": 1976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848900969, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848901272, "dur": 376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848901704, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848901988, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848902272, "dur": 545, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848902895, "dur": 1099, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@eb9759d45cf8\\Editor\\DefaultShaderIncludes.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754378848902818, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848904245, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378848904623, "dur": 2375, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378848907095, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378848907415, "dur": 4261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378848911756, "dur": 1262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848913056, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754378848913361, "dur": 871, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754378848914233, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848914310, "dur": 1488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1754378848915801, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848915908, "dur": 321, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378848916787, "dur": 4582311, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 4, "ts": 1754378853504620, "dur": 4432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853509061, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853509185, "dur": 3770, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853512958, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853513032, "dur": 3693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853516727, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853516798, "dur": 3578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853520377, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853520441, "dur": 3626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853524069, "dur": 506, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853524635, "dur": 3836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853528529, "dur": 4531, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853533062, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853533129, "dur": 3743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853536949, "dur": 3443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853540394, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853540469, "dur": 5976, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853546448, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853546679, "dur": 3961, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853550642, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853550758, "dur": 3654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853554413, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853554495, "dur": 5199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853559707, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853559823, "dur": 3675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853563501, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853563615, "dur": 3775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853567393, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853567508, "dur": 11806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853579316, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853579463, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853579665, "dur": 5189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853584856, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378853584966, "dur": 3808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754378853588871, "dur": 1241321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754378854830194, "dur": 579, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754378854849495, "dur": 3843, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 540, "tid": 74, "ts": 1754378947584676, "dur": 5885, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 540, "tid": 74, "ts": 1754378947596769, "dur": 129, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 540, "tid": 74, "ts": 1754378947597364, "dur": 57, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 540, "tid": 74, "ts": 1754378947590680, "dur": 6075, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947596988, "dur": 375, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947597464, "dur": 1303, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 540, "tid": 74, "ts": 1754378947571593, "dur": 29807, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}